volumes:
  weixin_agency_assistant_web_local_postgres_data: {}
  weixin_agency_assistant_web_local_postgres_data_backups: {}
  weixin_agency_assistant_web_local_redis_data: {}

services:
  django: &django
    build:
      context: .
      dockerfile: ./compose/local/django/Dockerfile
    image: weixin_agency_assistant_web_local_django
    container_name: weixin_agency_assistant_web_local_django
    depends_on:
      - postgres
      - redis
      - mailpit
    volumes:
      - /app/.venv
      - .:/app:z
    env_file:
      - ./.envs/.local/.django
      - ./.envs/.local/.postgres
    ports:
      - '8000:8000'
    command: /start

  postgres:
    build:
      context: .
      dockerfile: ./compose/production/postgres/Dockerfile
    image: weixin_agency_assistant_web_production_postgres
    container_name: weixin_agency_assistant_web_local_postgres
    volumes:
      - weixin_agency_assistant_web_local_postgres_data:/var/lib/postgresql/data
      - weixin_agency_assistant_web_local_postgres_data_backups:/backups
    env_file:
      - ./.envs/.local/.postgres

  mailpit:
    image: docker.io/axllent/mailpit:latest
    container_name: weixin_agency_assistant_web_local_mailpit
    ports:
      - "8025:8025"

  redis:
    image: docker.io/redis:6
    container_name: weixin_agency_assistant_web_local_redis
    volumes:
      - weixin_agency_assistant_web_local_redis_data:/data

  celeryworker:
    <<: *django
    image: weixin_agency_assistant_web_local_celeryworker
    container_name: weixin_agency_assistant_web_local_celeryworker
    depends_on:
      - redis
      - postgres
      - mailpit
    ports: []
    command: /start-celeryworker

  celerybeat:
    <<: *django
    image: weixin_agency_assistant_web_local_celerybeat
    container_name: weixin_agency_assistant_web_local_celerybeat
    depends_on:
      - redis
      - postgres
      - mailpit
    ports: []
    command: /start-celerybeat

  flower:
    <<: *django
    image: weixin_agency_assistant_web_local_flower
    container_name: weixin_agency_assistant_web_local_flower
    ports:
      - '5555:5555'
    command: /start-flower
