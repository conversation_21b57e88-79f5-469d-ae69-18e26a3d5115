from django.db import IntegrityError
from rest_framework.views import exception_handler
from rest_framework.response import Response as DRF_response
from rest_framework import status

from django.core import exceptions
from django.views import View
from django.http import response


def custom_exception_handler(exc: Exception, context: View) -> [response, None]:
    response = exception_handler(exc, context)

    if isinstance(exc, exceptions.ValidationError):
        data = exc.message_dict
        data["status_code"] = status.HTTP_400_BAD_REQUEST
        return DRF_response(data=data, status=status.HTTP_400_BAD_REQUEST, )

    if isinstance(exc, IntegrityError):
        data = {
            "detail": (
                'It seems there is a conflict between the data you are trying to save and your current data.'
                ' Please review your entries and try again.'
            ),
            "status_code": status.HTTP_400_BAD_REQUEST
        }
        return DRF_response(data=data, status=status.HTTP_400_BAD_REQUEST, )

    if response is not None:
        response.data['status_code'] = response.status_code

    return response
