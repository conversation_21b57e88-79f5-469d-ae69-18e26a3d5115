from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet

from core.models import Product

from .serializers import ProductSerializer


class ProductViewSet(ModelViewSet):
    serializer_class = ProductSerializer
    queryset = Product.objects.all()
    lookup_field = "pk"

    @action(detail=False, methods=['delete'])
    def delete_all(self, request):
        """
        删除所有商品
        """
        deleted_count, _ = Product.objects.all().delete()
        return Response(
            {
                'message': f'成功删除 {deleted_count} 个商品',
                'deleted_count': deleted_count
            },
            status=status.HTTP_200_OK
        )

    # def create(self, request, *args, **kwargs):
    #     """
    #     创建商品，如果相同 product_title 已存在则更新
    #     """
    #     # 从 product_data 中提取 title
    #     product_data = request.data.get('product_data', {})
    #     product_info = product_data.get('productInfo', {})
    #     title = product_info['title']
    #
    #     # 查找是否已存在相同 title 的商品
    #     try:
    #         existing_product = Product.objects.get(
    #             product_data__productInfo__title=title
    #         )
    #         # 如果存在，更新该商品
    #         serializer = self.get_serializer(existing_product, data=request.data, partial=False)
    #         serializer.is_valid(raise_exception=True)
    #         self.perform_update(serializer)
    #
    #         return Response(serializer.data, status=status.HTTP_200_OK)
    #
    #     except Product.DoesNotExist:
    #         # 如果不存在，创建新商品
    #         return super().create(request, *args, **kwargs)
