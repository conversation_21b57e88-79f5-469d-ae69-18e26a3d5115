# Generated by Django 5.2.7 on 2025-10-06 21:53

import django.core.serializers.json
import django.db.models.expressions
import django.db.models.fields.json
import django.db.models.functions.comparison
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_data', models.JSONField(blank=True, default=dict, encoder=django.core.serializers.json.DjangoJSONEncoder, null=True)),
                ('product_title', models.GeneratedField(db_persist=True, expression=django.db.models.fields.json.KeyTextTransform('product_data', 'productInfo__title'), output_field=models.CharField(max_length=1024), verbose_name='商品名称')),
                ('product_price', models.GeneratedField(db_persist=True, expression=django.db.models.expressions.CombinedExpression(django.db.models.functions.comparison.Cast(models.F('agency_intro__finderCnt'), models.PositiveIntegerField()), '/', models.Value(100)), output_field=models.DecimalField(decimal_places=2, max_digits=9), verbose_name='合作达人数')),
            ],
            options={
                'verbose_name': '商品',
                'verbose_name_plural': '商品',
            },
        ),
    ]
