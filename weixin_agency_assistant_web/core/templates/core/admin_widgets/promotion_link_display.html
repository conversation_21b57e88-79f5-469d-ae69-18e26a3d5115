{% load static %}

<style>
    .promotion-container {
        position: relative;
        display: inline-block;
    }

    .promotion-link {
        background: #007cba;
        color: white;
        padding: 4px 8px;
        border-radius: 3px;
        text-decoration: none;
        font-size: 12px;
        cursor: pointer;
    }

    .promotion-link:hover {
        background: #005a87;
        color: white;
        text-decoration: none;
    }

    .promotion-popup {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        width: 220px;
        margin-top: 5px;
        text-align: center;
    }

    /* 只有表格最后3行的popup向上显示 */
    tbody tr:nth-last-child(-n+3) .promotion-popup {
        top: auto;
        bottom: 100%;
        margin-top: 0;
        margin-bottom: 5px;
    }

    .qr-code {
        margin-bottom: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
    }

    .qr-code canvas {
        display: block;
        width: 200px;
        height: 200px;
    }

    .link-text {
        font-size: 11px;
        word-break: break-all;
        color: #666;
        margin-top: 5px;
    }
</style>

<div class="promotion-container"
     data-link="{{ obj.qrcode_link }}"
>
    <a href="#" class="promotion-link" onclick="showPromotionPopup(this); return false;">推广链接</a>

    <div class="promotion-popup">
        <div class="qr-code qrcode-container"></div>
        <div class="link-text">{{ obj.item_link|truncatechars:50 }}</div>
    </div>
</div>

<script src="{% static 'js/qrcode.min.js' %}"></script>
<script>
    // 显示推广弹窗并生成二维码
    function showPromotionPopup(element) {
        const container = element.closest('.promotion-container');
        const popup = container.querySelector('.promotion-popup');
        const qrcodeContainer = popup.querySelector('.qrcode-container');
        const qrcodeLink = container.getAttribute('data-link');

        // 清空之前的二维码
        qrcodeContainer.innerHTML = '';

        // 生成新的二维码
        if (qrcodeLink && typeof QRCode !== 'undefined') {
            try {
                new QRCode(qrcodeContainer, {
                    text: qrcodeLink,
                    width: 200,
                    height: 200,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    correctLevel: QRCode.CorrectLevel.L
                });
            } catch (error) {
                console.error('生成二维码失败:', error);
                qrcodeContainer.innerHTML = '<div style="color: #999; padding: 20px;">二维码生成失败</div>';
            }
        } else {
            qrcodeContainer.innerHTML = '<div style="color: #999; padding: 20px;">无法生成二维码</div>';
        }

        // 显示弹窗
        popup.style.display = 'block';

        // 点击其他地方关闭弹窗
        setTimeout(() => {
            document.addEventListener('click', function closePopupOnOutsideClick(e) {
                if (!container.contains(e.target)) {
                    popup.style.display = 'none';
                    document.removeEventListener('click', closePopupOnOutsideClick);
                }
            });
        }, 100);
    }
</script>
