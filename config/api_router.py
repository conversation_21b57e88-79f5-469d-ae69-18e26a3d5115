from django.conf import settings
from rest_framework.routers import <PERSON>fa<PERSON><PERSON>out<PERSON>
from rest_framework.routers import SimpleRouter

from weixin_agency_assistant_web.users.api.views import UserViewSet
from weixin_agency_assistant_web.core.api.views import ProductViewSet

router = DefaultRouter() if settings.DEBUG else SimpleRouter()

router.register("users", UserViewSet)
router.register("products", ProductViewSet,'product')

app_name = "api"
urlpatterns = router.urls
